<script setup lang="ts">
import { random } from 'lodash';
import { useMapStore } from '@stores';
import { CarAnimation } from '@components';
import gsap, { Power4, Linear } from 'gsap';
import { useGlobalData } from '@composables';

interface Props {
  fetched: boolean;
}

interface Emits {
  (event: 'success'): void;
}

const props = defineProps<Props>();
const emits = defineEmits<Emits>();

const tl = gsap.timeline();
const storeMap = useMapStore();

const { seasonCode, deploymentNumber } = useGlobalData();
const { isMapLoaded } = storeToRefs(storeMap);
const { t } = useI18n();
const { fetched } = toRefs(props);

const progress = ref(0);
const label = computed(() => (progress.value * 100).toFixed(0) + '%');

const TIPS = [
  'SAFETY_HINT1_TITLE',
  'SAFETY_HINT2_TITLE',
  'SAFETY_HINT3_TITLE',
  'SAFETY_HINT4_TITLE',
  'SAFETY_HINT5_TITLE',
  'SAFETY_HINT6_TITLE',
  'SAFETY_HINT7_TITLE',
];

const FLAVOUR = ['FLAVOUR_1', 'FLAVOUR_2', 'FLAVOUR_3', 'FLAVOUR_4', 'FLAVOUR_5'];

const tip = computed(() => t(TIPS[random(0, TIPS.length - 1)]!));
const flavour = computed(() => t(FLAVOUR[random(0, FLAVOUR.length - 1)]!));

watch([isMapLoaded, fetched], ([loadingVal, fetchedVal]) => {
  if (loadingVal && fetchedVal) {
    tl.kill();
    gsap.to(progress, {
      value: 1,
      ease: Linear.easeNone,
      duration: (100 - progress.value) / 100 + 0.5,
      onComplete: () => {
        storeMap.setIsLoading(false);
        emits('success');
      },
    });
  }
});

onMounted(async () => {
  await nextTick();
  tl.to(progress, {
    duration: 50,
    value: 0.6,
    ease: Power4.easeOut,
  }).to(progress, {
    duration: 30,
    value: 0.99,
    ease: Power4.easeOut,
  });
});

onBeforeUnmount(() => {
  tl.kill();
});
</script>

<template>
  <div class="text-center fixed inset-0 bg-[#0f132a]">
    <div class="fade-in loading fullscreen" :class="`season-${seasonCode.toLowerCase()}`">
      <TestingComponent>
        <div class="fixed z-50 text-center text-xs text-[#777] right-0 bottom-2.5 w-full">
          Build ({{ deploymentNumber }}):
          {{ new Date(Number(deploymentNumber) * 1000).toLocaleString() }}
        </div>
      </TestingComponent>

      <CarAnimation :assets="`sov/van/sqkii`" />
      <div class="absolute loading-card">
        <div class="relative w-full h-full flex flex-col gap-8">
          <div
            class="tip-box text-sm"
            v-html="
              t('LOADING_TIP', {
                TIP: tip,
              })
            "
          ></div>
          <div class="loading-progress">
            <div class="progress-bar" :style="{ width: `${progress * 100}%` }">
              <div class="text-sm font-bold progress-label">
                {{ label }}
              </div>
            </div>
          </div>
          <div class="text-base font-bold" v-html="flavour"></div>
        </div>
      </div>
    </div>
  </div>
</template>
<style lang="scss" scoped>
.loading {
  background-color: #0f132a;
  padding: 109vw 30px 0 30px;
  overflow-x: hidden;
  &.season-sg {
    background-size: 100% 112vw !important;
    background: url('/imgs/kv/sg.png') center -5vw no-repeat;
  }
  &.season-vn {
    background-size: 100% 112vw !important;
    background: url('/imgs/kv/vn.png') center -5vw no-repeat;
  }

  .loading-card {
    background: linear-gradient(180deg, rgba(15, 19, 42, 0) 0%, #0f132a 14.18%);
    width: 100%;
    padding: 18vw 30px 0 30px;
    top: 90vw;
    bottom: 0;
    left: 0;
    right: 0;
  }
  .loading-progress {
    position: relative;
    background: rgba($color: #4f0649, $alpha: 0.9);
    box-shadow: 0px -3px 5px rgba(0, 0, 0, 0.5);
    border-radius: 6px;
    width: 100%;
    height: 18px;
    .progress-bar {
      height: 100%;
      border-radius: 6px;
      max-width: 100%;
      background: linear-gradient(90deg, #7147cd 0%, #d834cf 100%);
      .progress-label {
        position: absolute;
        left: 50%;
        top: 50%;
        transform: translate(-50%, -50%);
      }
    }
  }
  .tip-box {
    padding: 10px;
    border-radius: 5px;
    border: 2px solid rgba(200, 107, 233, 0.8);
    background: linear-gradient(180deg, rgba(200, 107, 233, 0.64) 0%, rgba(187, 32, 201, 0.8) 100%);
  }
}

.fade-in {
  animation: fadeIn 0.1s ease-in-out;
}

@keyframes fadeIn {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}
</style>
