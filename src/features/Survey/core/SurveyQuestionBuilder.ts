import { SurveyQuestion } from '@types';

export class SurveyQuestionBuilder {
  private question: Partial<SurveyQuestion> = {};

  constructor(private t: (key: string) => string) {}

  order(order: number): this {
    this.question.order = order;
    return this;
  }

  id(titleKey: string): this {
    this.question.id = this.generateIdFromTitle(titleKey);
    return this;
  }

  private generateIdFromTitle(titleKey: string): string {
    return titleKey.toLowerCase().replace(/[^a-z0-9]/g, '_');
  }

  title(key: string): this {
    this.question.q = this.t(key);
    return this;
  }

  subtitle(key: string): this {
    this.question.sub_q = this.t(key);
    return this;
  }

  type(type: SurveyQuestion['type']): this {
    this.question.type = type;
    return this;
  }

  options(optionKeys: string[]): this {
    this.question.a = optionKeys.map((key) => ({ value: this.t(key) }));
    return this;
  }

  textAreaOptions(options: Array<{ key?: string; title?: string }>): this {
    this.question.a = options.map((option) => {
      if (option.key) {
        return { value: this.t(option.key) };
      } else if (option.title) {
        return {
          title: this.t(option.title),
          type: 'area' as const,
          value: '',
        };
      }
      return { value: '' };
    });
    return this;
  }

  rating(minKey: string, maxKey: string): this {
    this.question.a = [{ value: 0 }];
    this.question.min_rate_text = this.t(minKey);
    this.question.max_rate_text = this.t(maxKey);
    return this;
  }

  textArea(): this {
    this.question.a = [{ value: '' }];
    return this;
  }

  multiple(isMultiple = true): this {
    this.question.multiple = isMultiple;
    return this;
  }

  condition(
    conditionFn: (currentQuestion?: SurveyQuestion, allQuestions?: SurveyQuestion[]) => string,
  ): this {
    this.question.condition = { next: conditionFn };
    return this;
  }

  selected(value: any): this {
    this.question.selected = value;
    return this;
  }

  build(): SurveyQuestion {
    if (!this.question.id || !this.question.q || !this.question.type) {
      throw new Error('Question must have id, title, and type');
    }
    return this.question as SurveyQuestion;
  }
}
