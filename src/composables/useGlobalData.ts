import { useUserStore } from '@stores';
import { TUserLang } from '@types';
import dayjs from 'dayjs';

export function useGlobalData() {
  const storeUser = useUserStore();

  const { user, gameFlags, currentSeason } = storeToRefs(storeUser);

  const seasonCode = computed(() => process.env.APP_COUNTRY_CODE || 'SG');

  const campaignName = computed(() => process.env.APP_CAMPAIGN_NAME || 'SG');

  const deploymentNumber = computed(() => process.env.APP_DEPLOYMENT_NUMBER);

  const appLanguageCode = computed(() => process.env.APP_LANGUAGE_CODE as TUserLang);

  const isTestingEnv = computed(() => !!process.env.APP_TESTING_ENV);

  const turnstileDumyToken = computed(() => process.env.APP_TURNSTILE_DUMMY_TOKEN as string);

  const isUserLogged = computed(() => !!user.value?.mobile_number);

  const triggerEndGameSurvey = computed(
    () => gameFlags.value?.end_game_survey && !user.value?.end_survey,
  );

  const triggerMidGameSurvey = computed(() => {
    if (!user.value || !currentSeason.value) return false;
    if (user.value.survey) return false;
    if (+new Date(currentSeason.value.start_at) > +new Date()) return false;

    const today = dayjs();
    const startHunt = dayjs(currentSeason.value.start_at);
    const joinedHunt = dayjs(user.value.created_at);
    const daysSinceJoin = today.diff(joinedHunt, 'day');
    const daysSinceHunt = today.diff(startHunt, 'day');

    if (daysSinceHunt < 2) return false;
    return daysSinceJoin >= 3;
  });

  return {
    seasonCode,
    campaignName,
    appLanguageCode,
    deploymentNumber,
    isTestingEnv,
    turnstileDumyToken,
    isUserLogged,
    triggerEndGameSurvey,
    triggerMidGameSurvey,
  };
}
