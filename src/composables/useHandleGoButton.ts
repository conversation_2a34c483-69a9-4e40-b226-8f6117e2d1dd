import { useBAStore } from '@stores';
import { useFetchQueries, useNow, usePageTracker } from '@composables';
import { BrandAction, ActivatingTimeMission } from '@types';
import { useOfferWallVisitWebMutation } from '@services';

type FROM = 'mission_timed' | 'brand_action';

export const useHandleGoButton = (from: FROM = 'brand_action') => {
  const visitWebMutation = useOfferWallVisitWebMutation();

  const storeBA = useBAStore();
  const now = useNow();

  const { push, openDialog, activePage } = useMicroRoute();
  const { user_brand_actions } = storeToRefs(storeBA);
  const { offerWallQuery } = useFetchQueries();
  const { tracker } = usePageTracker();

  const isOfferWallPage = computed(() => {
    return activePage.value === 'offer_wall';
  });

  const promoAction = (item: BrandAction, type = 'promocode') => {
    const steps = item.instructions;
    if (steps.every((el) => el))
      push(isOfferWallPage.value ? 'ba_instruction' : 'offer_wall/ba_instruction', {
        data: item,
      });
    else
      openDialog('enter_promo', {
        data: item,
        type,
      });
  };

  const locked_item = (item: BrandAction) => {
    const value = user_brand_actions.value.find(
      (brand) =>
        item.status === 'new' &&
        brand.ba_unique_id === item.unique_id &&
        brand.lock_until &&
        +new Date(brand.lock_until) >= now.value,
    );

    return value;
  };

  async function handleVisitWebAction(item: BrandAction | ActivatingTimeMission): Promise<void> {
    const id = 'brandAction' in item ? item.brandAction._id : item._id;
    await visitWebMutation.mutateAsync(id);
    await offerWallQuery.refetch();
  }

  async function handleGoButton(ba: BrandAction): Promise<void> {
    //  type: ba.type,
    //   brand_action_id: ba._id,
    //   ba_unique_id: ba.ba_unique_id,
    //   from,

    tracker({
      id: 'offer_wall_action',
      action: 'click',
      data: {
        type: ba.type,
        brand_action_id: ba._id,
        ba_unique_id: ba.ba_unique_id,
        status: ba.status,
        from,
      },
    });

    const lockedItem = locked_item(ba);
    if (lockedItem) {
      openDialog('ba_status', {
        brandAction: lockedItem,
      });
      return;
    }

    switch (ba.type) {
      case 'receipt_verification':
      case 'client_verify':
      case 'scan_qrcode':
        push(isOfferWallPage.value ? 'ba_instruction' : 'offer_wall/ba_instruction', {
          data: ba,
        });
        break;
      case 'promo_code':
      case 'enter_promo_code':
        promoAction(ba);
        break;
      case 'enter_barcode':
        promoAction(ba, 'barcode');
        break;
      case 'open_external_link':
      case 'visit_web':
        if (ba.unique_id === 'spf_4')
          push(isOfferWallPage.value ? 'ba_instruction' : 'offer_wall/ba_instruction', {
            data: ba,
          });
        else await handleVisitWebAction(ba);
        break;
      case 'survey':
        openDialog('ba_survey', {
          dataBrand: ba,
        });
        break;

      case 'sqkii_voucher':
      case 'use_sqkii_voucher':
        push('sqkii_vouchers');
        break;
      case 'spf_quiz':
        openDialog('spf_welcome', {
          skipQuiz: from === 'mission_timed',
          idFromOfferWall: ba.group_id,
        });
        break;
      case 'read_spf_message':
      case 'spf_sharing':
        openDialog('spf_welcome', {
          skipQuiz: true,
          idFromOfferWall: ba.group_id,
        });
        break;
      case 'location_based':
        push('missions');
        break;
      default:
        alert(`${ba.type} Not implemented yet`);
        break;
    }
  }

  return { handleGoButton, locked_item, handleVisitWebAction };
};
